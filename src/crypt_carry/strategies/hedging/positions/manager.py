"""
套保持仓管理器 - 管理跨交易所的配对持仓，计算实时盈亏，处理持仓调整和平仓
"""
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from crypt_carry.strategies.hedging.cross_exchange_coordinator import HedgingPair, CrossExchangeOrder
from crypt_carry.utils.ding_talk_manager import DingTalkManager
from crypt_carry.utils.logger_config import get_hedging_strategy_logger

logger = get_hedging_strategy_logger(__name__)


class PositionStatus(Enum):
    """持仓状态"""
    ACTIVE = "active"  # 活跃
    CLOSING = "closing"  # 平仓中
    CLOSED = "closed"  # 已平仓
    ERROR = "error"  # 错误状态


@dataclass
class PnLData:
    """盈亏数据"""
    unrealized_pnl: float = 0.0  # 未实现盈亏(USDT)
    realized_pnl: float = 0.0  # 已实现盈亏(USDT)
    total_pnl: float = 0.0  # 总盈亏(USDT)
    pnl_percentage: float = 0.0  # 盈亏百分比
    long_pnl: float = 0.0  # 多头盈亏
    short_pnl: float = 0.0  # 空头盈亏
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


@dataclass
class ManagedPosition:
    """管理的持仓"""
    hedging_pair: HedgingPair
    status: PositionStatus
    created_at: datetime
    updated_at: datetime
    pnl_data: PnLData
    auto_close_enabled: bool = True
    stop_loss_price: Optional[float] = None
    take_profit_price: Optional[float] = None
    notes: str = ""


class HedgingPositionManager:
    """套保持仓管理器"""

    def __init__(self):
        """初始化套保持仓管理器"""
        self.positions: Dict[str, ManagedPosition] = {}  # pair_id -> ManagedPosition
        self.position_history: List[ManagedPosition] = []

        # 配置参数
        self.pnl_update_interval = 10  # 盈亏更新间隔(秒)
        self.auto_close_threshold = 0.05  # 自动平仓阈值(5%)
        self.stop_loss_threshold = -0.1  # 止损阈值(-10%)
        self.take_profit_threshold = 0.2  # 止盈阈值(20%)

        # 钉钉消息管理器
        self.ding_talk_manager = DingTalkManager.get_instance()

        # 监控状态
        self.is_monitoring = False
        self._stop_event = asyncio.Event()

    async def initialize(self):
        """初始化持仓管理器"""
        try:
            logger.info("初始化套保持仓管理器...")

            # 加载历史持仓数据
            await self._load_position_history()

            logger.info("套保持仓管理器初始化完成")

        except Exception as e:
            logger.error(f"初始化套保持仓管理器失败: {str(e)}")
            raise

    async def start_monitoring(self):
        """启动持仓监控"""
        try:
            self.is_monitoring = True
            logger.info("启动套保持仓监控")

            while self.is_monitoring:
                try:
                    # 更新所有持仓的盈亏
                    await self._update_all_pnl()

                    # 检查自动平仓条件
                    await self._check_auto_close_conditions()

                    # 检查止损止盈
                    await self._check_stop_loss_take_profit()

                    # 清理已完成的持仓
                    await self._cleanup_completed_positions()

                    # 等待下次更新
                    await asyncio.sleep(self.pnl_update_interval)

                except Exception as e:
                    logger.error(f"持仓监控异常: {str(e)}")
                    await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"持仓监控启动失败: {str(e)}")
        finally:
            self.is_monitoring = False

    async def stop_monitoring(self):
        """停止持仓监控"""
        logger.info("停止套保持仓监控")
        self.is_monitoring = False
        self._stop_event.set()

    async def add_position(self, hedging_pair: HedgingPair) -> bool:
        """添加新的套保持仓

        Args:
            hedging_pair: 套保交易对

        Returns:
            bool: 是否添加成功
        """
        try:
            if hedging_pair.pair_id in self.positions:
                logger.warning(f"持仓已存在: {hedging_pair.pair_id}")
                return False

            # 创建管理的持仓
            managed_position = ManagedPosition(
                hedging_pair=hedging_pair,
                status=PositionStatus.ACTIVE,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                pnl_data=PnLData()
            )

            # 添加到持仓字典
            self.positions[hedging_pair.pair_id] = managed_position

            # 初始计算盈亏
            await self._update_position_pnl(managed_position)

            logger.info(f"添加套保持仓: {hedging_pair.pair_id}")

            # 发送通知
            message = (
                f"新增套保持仓\n"
                f"配对ID: {hedging_pair.pair_id}\n"
                f"多头: {hedging_pair.long_order.exchange} {hedging_pair.long_order.symbol}\n"
                f"空头: {hedging_pair.short_order.exchange} {hedging_pair.short_order.symbol}\n"
                f"目标金额: {hedging_pair.target_amount:.2f} USDT"
            )
            self.ding_talk_manager.send_message(message, prefix="持仓管理")

            return True

        except Exception as e:
            logger.error(f"添加套保持仓失败: {str(e)}")
            return False

    async def close_position(self, pair_id: str, reason: str = "手动平仓") -> bool:
        """平仓指定的套保持仓

        Args:
            pair_id: 配对ID
            reason: 平仓原因

        Returns:
            bool: 是否平仓成功
        """
        try:
            if pair_id not in self.positions:
                logger.warning(f"持仓不存在: {pair_id}")
                return False

            managed_position = self.positions[pair_id]

            if managed_position.status != PositionStatus.ACTIVE:
                logger.warning(f"持仓状态不允许平仓: {pair_id}, 状态: {managed_position.status}")
                return False

            # 更新状态为平仓中
            managed_position.status = PositionStatus.CLOSING
            managed_position.updated_at = datetime.now(timezone.utc)
            managed_position.notes = f"平仓原因: {reason}"

            logger.info(f"开始平仓套保持仓: {pair_id}, 原因: {reason}")

            # TODO: 实现实际的平仓逻辑
            # 这里需要调用跨交易所协调器来执行平仓操作

            # 模拟平仓成功
            managed_position.status = PositionStatus.CLOSED
            managed_position.updated_at = datetime.now(timezone.utc)

            # 计算最终盈亏
            await self._update_position_pnl(managed_position)
            managed_position.pnl_data.realized_pnl = managed_position.pnl_data.total_pnl
            managed_position.pnl_data.unrealized_pnl = 0.0

            # 添加到历史记录
            self.position_history.append(managed_position)

            # 发送平仓通知
            message = (
                f"套保持仓已平仓\n"
                f"配对ID: {pair_id}\n"
                f"平仓原因: {reason}\n"
                f"实现盈亏: {managed_position.pnl_data.realized_pnl:.2f} USDT\n"
                f"盈亏比例: {managed_position.pnl_data.pnl_percentage:.2%}"
            )
            self.ding_talk_manager.send_message(message, prefix="持仓平仓")

            logger.info(f"套保持仓平仓完成: {pair_id}")
            return True

        except Exception as e:
            logger.error(f"平仓套保持仓失败: {str(e)}")
            return False

    async def _update_all_pnl(self):
        """更新所有持仓的盈亏"""
        try:
            for managed_position in self.positions.values():
                if managed_position.status == PositionStatus.ACTIVE:
                    await self._update_position_pnl(managed_position)

        except Exception as e:
            logger.error(f"更新所有持仓盈亏失败: {str(e)}")

    async def _update_position_pnl(self, managed_position: ManagedPosition):
        """更新单个持仓的盈亏

        Args:
            managed_position: 管理的持仓
        """
        try:
            hedging_pair = managed_position.hedging_pair

            # TODO: 获取当前价格并计算盈亏
            # 这里需要从市场数据获取当前价格

            # 模拟盈亏计算
            # 多头盈亏 = (当前价格 - 开仓价格) * 数量
            # 空头盈亏 = (开仓价格 - 当前价格) * 数量

            # 模拟数据
            long_pnl = 0.0  # 多头盈亏
            short_pnl = 0.0  # 空头盈亏
            total_pnl = long_pnl + short_pnl
            pnl_percentage = total_pnl / hedging_pair.target_amount if hedging_pair.target_amount > 0 else 0.0

            # 更新盈亏数据
            managed_position.pnl_data = PnLData(
                unrealized_pnl=total_pnl,
                realized_pnl=managed_position.pnl_data.realized_pnl,
                total_pnl=total_pnl + managed_position.pnl_data.realized_pnl,
                pnl_percentage=pnl_percentage,
                long_pnl=long_pnl,
                short_pnl=short_pnl,
                timestamp=datetime.now(timezone.utc)
            )

            managed_position.updated_at = datetime.now(timezone.utc)

        except Exception as e:
            logger.error(f"更新持仓盈亏失败: {str(e)}")

    async def _check_auto_close_conditions(self):
        """检查自动平仓条件"""
        try:
            for pair_id, managed_position in self.positions.items():
                if (managed_position.status == PositionStatus.ACTIVE and
                    managed_position.auto_close_enabled):

                    # 检查是否达到自动平仓条件
                    # 这里可以添加各种自动平仓逻辑
                    # 例如：持仓时间过长、市场条件变化等

                    # 示例：如果持仓超过24小时，考虑平仓
                    position_age = datetime.now(timezone.utc) - managed_position.created_at
                    if position_age > timedelta(hours=24):
                        logger.info(f"持仓时间过长，考虑自动平仓: {pair_id}")
                        # await self.close_position(pair_id, "持仓时间过长")

        except Exception as e:
            logger.error(f"检查自动平仓条件失败: {str(e)}")

    async def _check_stop_loss_take_profit(self):
        """检查止损止盈"""
        try:
            for pair_id, managed_position in self.positions.items():
                if managed_position.status == PositionStatus.ACTIVE:
                    pnl_percentage = managed_position.pnl_data.pnl_percentage

                    # 检查止损
                    if pnl_percentage <= self.stop_loss_threshold:
                        logger.warning(f"触发止损: {pair_id}, 盈亏: {pnl_percentage:.2%}")
                        await self.close_position(pair_id, f"止损平仓 (盈亏: {pnl_percentage:.2%})")
                        continue

                    # 检查止盈
                    if pnl_percentage >= self.take_profit_threshold:
                        logger.info(f"触发止盈: {pair_id}, 盈亏: {pnl_percentage:.2%}")
                        await self.close_position(pair_id, f"止盈平仓 (盈亏: {pnl_percentage:.2%})")
                        continue

        except Exception as e:
            logger.error(f"检查止损止盈失败: {str(e)}")

    async def _cleanup_completed_positions(self):
        """清理已完成的持仓"""
        try:
            completed_positions = []

            for pair_id, managed_position in self.positions.items():
                if managed_position.status in [PositionStatus.CLOSED, PositionStatus.ERROR]:
                    # 检查是否已经在历史记录中
                    if not any(p.hedging_pair.pair_id == pair_id for p in self.position_history):
                        self.position_history.append(managed_position)

                    completed_positions.append(pair_id)

            # 从活跃持仓中移除
            for pair_id in completed_positions:
                self.positions.pop(pair_id, None)

            if completed_positions:
                logger.info(f"清理已完成持仓: {len(completed_positions)} 个")

        except Exception as e:
            logger.error(f"清理已完成持仓失败: {str(e)}")

    async def _load_position_history(self):
        """加载历史持仓数据"""
        try:
            # TODO: 从数据库或文件加载历史持仓数据
            logger.info("加载历史持仓数据...")

        except Exception as e:
            logger.error(f"加载历史持仓数据失败: {str(e)}")

    def get_active_positions(self) -> Dict[str, ManagedPosition]:
        """获取所有活跃持仓"""
        return {k: v for k, v in self.positions.items() if v.status == PositionStatus.ACTIVE}

    def get_position(self, pair_id: str) -> Optional[ManagedPosition]:
        """获取指定持仓

        Args:
            pair_id: 配对ID

        Returns:
            ManagedPosition: 持仓对象，不存在返回None
        """
        return self.positions.get(pair_id)

    def get_position_summary(self) -> Dict:
        """获取持仓摘要"""
        try:
            active_positions = self.get_active_positions()

            total_positions = len(active_positions)
            total_exposure = sum(p.hedging_pair.target_amount for p in active_positions.values())
            total_unrealized_pnl = sum(p.pnl_data.unrealized_pnl for p in active_positions.values())
            total_realized_pnl = sum(p.pnl_data.realized_pnl for p in self.position_history)

            return {
                'total_active_positions': total_positions,
                'total_exposure': total_exposure,
                'total_unrealized_pnl': total_unrealized_pnl,
                'total_realized_pnl': total_realized_pnl,
                'total_pnl': total_unrealized_pnl + total_realized_pnl,
                'average_pnl_percentage': (total_unrealized_pnl / total_exposure * 100) if total_exposure > 0 else 0,
                'positions_by_status': {
                    'active': len([p for p in self.positions.values() if p.status == PositionStatus.ACTIVE]),
                    'closing': len([p for p in self.positions.values() if p.status == PositionStatus.CLOSING]),
                    'closed': len(self.position_history),
                    'error': len([p for p in self.positions.values() if p.status == PositionStatus.ERROR])
                }
            }

        except Exception as e:
            logger.error(f"获取持仓摘要失败: {str(e)}")
            return {}

    async def cleanup(self):
        """清理资源"""
        try:
            # 停止监控
            await self.stop_monitoring()

            # 保存持仓数据
            await self._save_position_data()

            logger.info("套保持仓管理器清理完成")

        except Exception as e:
            logger.error(f"清理套保持仓管理器失败: {str(e)}")

    async def _save_position_data(self):
        """保存持仓数据"""
        try:
            # TODO: 保存持仓数据到数据库或文件
            logger.info("保存持仓数据...")

        except Exception as e:
            logger.error(f"保存持仓数据失败: {str(e)}")
