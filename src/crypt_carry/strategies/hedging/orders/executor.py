"""
套保任务执行器
负责轮询和执行拆单任务，调用现有的下单功能
"""
import asyncio
import threading
from datetime import datetime, timezone
from typing import List, Dict, Optional, Any
import time

from crypt_carry.strategies.hedging.orders.cache_manager import SplitTask, ExecutionType, get_order_cache_manager
from crypt_carry.strategies.hedging.orders.condition_checker import get_condition_checker
from crypt_carry.strategies.base_strategy import BaseStrategy
from crypt_carry.utils.logger_config import get_hedging_strategy_logger

logger = get_hedging_strategy_logger(__name__)


class TaskExecutor:
    """套保任务执行器"""

    def __init__(self, base_strategy: Optional[BaseStrategy] = None):
        """初始化任务执行器

        Args:
            base_strategy: 基础策略实例，用于调用现有的下单功能
        """
        self.base_strategy = base_strategy
        self.order_cache = get_order_cache_manager()
        self.condition_checker = get_condition_checker()  # 新增：条件检查服务
        self.is_running = False
        self.execution_interval = 1  # 执行间隔(秒)
        self.max_concurrent_tasks = 3  # 最大并发任务数
        self.current_executing_tasks = 0
        self._execution_task = None  # 保存执行任务的引用

        logger.info("套保任务执行器初始化完成")

    async def start(self):
        """启动任务执行器"""
        if self.is_running:
            logger.warning("任务执行器已在运行")
            return

        self.is_running = True
        logger.info("任务执行器已启动")

        # 启动主执行循环并保存任务引用
        self._execution_task = asyncio.create_task(self._execution_loop())

        # 确保任务不会被垃圾回收
        self._execution_task.add_done_callback(self._on_execution_task_done)

        logger.info(f"任务执行器异步任务已创建: {self._execution_task}")

    def _on_execution_task_done(self, task):
        """执行任务完成回调"""
        if task.cancelled():
            logger.warning("任务执行器异步任务被取消")
        elif task.exception():
            logger.error(f"任务执行器异步任务异常: {task.exception()}")
            import traceback
            logger.error(f"任务异常详情: {''.join(traceback.format_exception(type(task.exception()), task.exception(), task.exception().__traceback__))}")
        else:
            logger.info("任务执行器异步任务正常完成")

    async def stop(self):
        """停止任务执行器"""
        self.is_running = False

        # 取消执行任务
        if self._execution_task and not self._execution_task.done():
            self._execution_task.cancel()
            try:
                await self._execution_task
            except asyncio.CancelledError:
                pass

        logger.info("任务执行器已停止")

    async def _execution_loop(self):
        """主执行循环"""
        logger.info("任务执行器主循环已启动")
        loop_count = 0

        try:
            while self.is_running:
                try:
                    loop_count += 1
                    logger.debug(f"执行循环第 {loop_count} 次")

                    # 获取待执行的任务
                    pending_tasks = await self.order_cache.get_pending_split_tasks()

                    if pending_tasks:
                        logger.info(f"发现 {len(pending_tasks)} 个待执行任务")

                        # 执行任务（控制并发数量）
                        await self._execute_tasks_batch(pending_tasks)
                    else:
                        logger.debug("当前没有待执行任务")

                    # 等待下次执行
                    logger.debug(f"等待 {self.execution_interval} 秒后进行下次检查")
                    await asyncio.sleep(self.execution_interval)

                except Exception as e:
                    logger.error(f"执行循环异常: {str(e)}")
                    import traceback
                    logger.error(f"详细错误信息: {traceback.format_exc()}")
                    await asyncio.sleep(5)  # 异常时等待更长时间

        except Exception as e:
            logger.error(f"执行循环外层异常: {str(e)}")
            import traceback
            logger.error(f"外层异常详细信息: {traceback.format_exc()}")
        finally:
            logger.info(f"任务执行器主循环已停止，共执行了 {loop_count} 次循环")

    async def _execute_tasks_batch(self, tasks: List[SplitTask]):
        """批量执行任务

        Args:
            tasks: 待执行的任务列表
        """
        try:
            # 控制并发数量
            available_slots = self.max_concurrent_tasks - self.current_executing_tasks
            if available_slots <= 0:
                logger.debug("已达到最大并发数，等待下次执行")
                return

            # 选择要执行的任务
            tasks_to_execute = tasks[:available_slots]

            # 并发执行任务
            execution_tasks = []
            for task in tasks_to_execute:
                execution_tasks.append(self._execute_single_task(task))

            if execution_tasks:
                await asyncio.gather(*execution_tasks, return_exceptions=True)

        except Exception as e:
            logger.error(f"批量执行任务失败: {str(e)}")

    async def _execute_single_task(self, task: SplitTask):
        """执行单个任务

        Args:
            task: 拆单任务
        """
        try:
            self.current_executing_tasks += 1
            logger.info(f"开始执行任务: {task.task_id}")

            # 更新任务状态为执行中
            await self.order_cache.update_split_task_status(task.task_id, "executing")

            # 新增：检查是否满足触发条件
            conditions_met = await self._check_execution_conditions(task)
            if not conditions_met:
                # 条件未满足，将任务状态改回pending，等待下次检查
                await self.order_cache.update_split_task_status(task.task_id, "pending")
                logger.debug(f"任务条件未满足，等待下次检查: {task.task_id}")
                return

            logger.info(f"任务条件已满足，开始执行下单: {task.task_id}")

            # 执行套保下单
            execution_result = await self._execute_hedging_order(task)

            # 更新任务状态和结果
            if execution_result.get('success', False):
                await self.order_cache.update_split_task_status(
                    task.task_id, "completed", execution_result
                )
                logger.info(f"任务执行成功: {task.task_id}")
            else:
                await self.order_cache.update_split_task_status(
                    task.task_id, "failed", execution_result
                )
                logger.error(f"任务执行失败: {task.task_id}, 错误: {execution_result.get('error')}")

        except Exception as e:
            logger.error(f"执行任务异常: {task.task_id}, 错误: {str(e)}")
            await self.order_cache.update_split_task_status(
                task.task_id, "failed", {'error': str(e)}
            )
        finally:
            self.current_executing_tasks -= 1

    async def _check_execution_conditions(self, task: SplitTask) -> bool:
        """检查任务的执行条件

        Args:
            task: 拆单任务

        Returns:
            bool: 是否满足执行条件
        """
        try:
            # 检查执行类型
            if hasattr(task, 'execution_type') and task.execution_type == ExecutionType.IMMEDIATE:
                # 立即执行类型，无需检查条件
                logger.debug(f"任务为立即执行类型: {task.task_id}")
                return True

            # 检查是否有触发条件
            if not hasattr(task, 'conditions') or not task.conditions:
                # 没有条件，立即执行
                logger.debug(f"任务无触发条件，立即执行: {task.task_id}")
                return True

            # 使用条件检查服务检查所有条件
            conditions_met = await self.condition_checker.check_conditions(
                task.conditions,
                task.long_exchange, task.long_symbol, task.long_is_spot,
                task.short_exchange, task.short_symbol, task.short_is_spot
            )

            if conditions_met:
                logger.info(f"任务触发条件已满足: {task.task_id}")
            else:
                logger.debug(f"任务触发条件未满足: {task.task_id}")

            return conditions_met

        except Exception as e:
            logger.error(f"检查任务执行条件失败: {task.task_id}, 错误: {str(e)}")
            # 出现异常时，为了安全起见，不执行任务
            return False

    async def _execute_hedging_order(self, task: SplitTask) -> Dict[str, Any]:
        """执行套保下单

        调用现有的资金费率套利下单功能

        Args:
            task: 拆单任务

        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            logger.info(f"执行套保下单: {task.task_id}, 金额: {task.amount} {task.amount_currency}")

            # 检查是否有基础策略实例
            if not self.base_strategy:
                logger.warning("基础策略未初始化，使用模拟模式")
                return await self._simulate_hedging_execution(task)

            # 调用现有的下单功能
            # 这里需要根据您现有的资金费率套利下单接口进行调用
            result = await self._call_existing_order_function(task)

            return result

        except Exception as e:
            logger.error(f"执行套保下单失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'task_id': task.task_id,
                'executed_at': datetime.now(timezone.utc).isoformat()
            }

    async def _call_existing_order_function(self, task: SplitTask) -> Dict[str, Any]:
        """调用现有的下单功能

        Args:
            task: 拆单任务

        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            # TODO: 这里需要根据您现有的资金费率套利下单接口进行实现
            # 示例调用（需要根据实际接口调整）:

            # 1. 准备下单参数
            order_params = {
                'long_exchange': task.long_exchange,
                'long_symbol': task.long_symbol,
                'long_is_spot': task.long_is_spot,
                'short_exchange': task.short_exchange,
                'short_symbol': task.short_symbol,
                'short_is_spot': task.short_is_spot,
                'amount': task.amount,
                'amount_currency': task.amount_currency
            }

            # 2. 调用现有的下单方法
            # 假设您有一个 execute_arbitrage_order 方法
            if hasattr(self.base_strategy, 'execute_arbitrage_order'):
                result = await self.base_strategy.execute_arbitrage_order(**order_params)
            elif hasattr(self.base_strategy, 'place_hedging_orders'):
                result = await self.base_strategy.place_hedging_orders(**order_params)
            else:
                # 如果没有找到合适的方法，使用模拟模式
                logger.warning("未找到现有的下单方法，使用模拟模式")
                return await self._simulate_hedging_execution(task)

            # 3. 格式化返回结果
            return {
                'success': result.get('success', False),
                'task_id': task.task_id,
                'filled_amount': task.amount,  # 假设全部成交
                'long_order': result.get('long_order'),
                'short_order': result.get('short_order'),
                'execution_price': result.get('execution_price'),
                'slippage': result.get('slippage', 0),
                'fees': result.get('fees', 0),
                'executed_at': datetime.now(timezone.utc).isoformat(),
                'raw_result': result
            }

        except Exception as e:
            logger.error(f"调用现有下单功能失败: {str(e)}")
            return {
                'success': False,
                'error': f'调用现有下单功能失败: {str(e)}',
                'task_id': task.task_id,
                'executed_at': datetime.now(timezone.utc).isoformat()
            }

    async def _simulate_hedging_execution(self, task: SplitTask) -> Dict[str, Any]:
        """模拟套保执行

        Args:
            task: 拆单任务

        Returns:
            Dict[str, Any]: 模拟执行结果
        """
        try:
            # 模拟执行时间
            await asyncio.sleep(1)

            # 模拟成功的执行结果
            return {
                'success': True,
                'task_id': task.task_id,
                'filled_amount': task.amount,
                'long_order': {
                    'exchange': task.long_exchange,
                    'symbol': task.long_symbol,
                    'side': 'buy',
                    'amount': task.amount,
                    'status': 'filled',
                    'order_id': f"long_{task.task_id}_{int(time.time())}"
                },
                'short_order': {
                    'exchange': task.short_exchange,
                    'symbol': task.short_symbol,
                    'side': 'sell',
                    'amount': task.amount,
                    'status': 'filled',
                    'order_id': f"short_{task.task_id}_{int(time.time())}"
                },
                'execution_price': 50000.0,  # 模拟价格
                'slippage': 0.001,  # 0.1% 滑点
                'fees': task.amount * 0.001,  # 0.1% 手续费
                'executed_at': datetime.now(timezone.utc).isoformat(),
                'note': '模拟执行 - 未实际下单'
            }

        except Exception as e:
            logger.error(f"模拟执行失败: {str(e)}")
            return {
                'success': False,
                'error': f'模拟执行失败: {str(e)}',
                'task_id': task.task_id,
                'executed_at': datetime.now(timezone.utc).isoformat()
            }

    async def get_execution_status(self) -> Dict[str, Any]:
        """获取执行器状态

        Returns:
            Dict[str, Any]: 执行器状态信息
        """
        try:
            pending_tasks = await self.order_cache.get_pending_split_tasks()

            return {
                'is_running': self.is_running,
                'current_executing_tasks': self.current_executing_tasks,
                'max_concurrent_tasks': self.max_concurrent_tasks,
                'pending_tasks_count': len(pending_tasks),
                'execution_interval': self.execution_interval,
                'status': 'running' if self.is_running else 'stopped'
            }

        except Exception as e:
            logger.error(f"获取执行器状态失败: {str(e)}")
            return {'error': str(e)}

    async def force_execute_task(self, task_id: str) -> Dict[str, Any]:
        """强制执行指定任务

        Args:
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            # 获取任务
            pending_tasks = await self.order_cache.get_pending_split_tasks()
            target_task = None

            for task in pending_tasks:
                if task.task_id == task_id:
                    target_task = task
                    break

            if not target_task:
                return {
                    'success': False,
                    'error': f'任务不存在或不在待执行状态: {task_id}'
                }

            # 强制执行
            await self._execute_single_task(target_task)

            return {
                'success': True,
                'message': f'任务已强制执行: {task_id}'
            }

        except Exception as e:
            logger.error(f"强制执行任务失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }


# 全局单例
_task_executor = None

def get_task_executor() -> TaskExecutor:
    """获取任务执行器单例"""
    global _task_executor
    if _task_executor is None:
        _task_executor = TaskExecutor()
    return _task_executor
