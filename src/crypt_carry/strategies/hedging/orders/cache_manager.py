"""
套保订单缓存管理器
负责订单的缓存、状态管理和持久化
"""
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class OrderStatus(Enum):
    """订单状态"""
    CREATED = "created"           # 已创建
    SPLITTING = "splitting"       # 拆单中
    SPLIT_COMPLETED = "split_completed"  # 拆单完成
    EXECUTING = "executing"       # 执行中
    PARTIALLY_FILLED = "partially_filled"  # 部分成交
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"            # 失败
    CANCELLED = "cancelled"      # 已取消


class ExecutionType(Enum):
    """执行类型"""
    IMMEDIATE = "immediate"      # 立即执行
    CONDITIONAL = "conditional"  # 条件执行


@dataclass
class HedgingOrder:
    """套保订单"""
    order_id: str
    execution_type: ExecutionType
    conditions: List[Dict]  # 触发条件列表
    long_exchange: str
    long_symbol: str
    long_is_spot: bool
    short_exchange: str
    short_symbol: str
    short_is_spot: bool
    amount: float
    amount_currency: str
    priority: int = 1
    status: OrderStatus = OrderStatus.CREATED
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    # 拆单相关
    split_tasks: List[Dict] = None  # 拆单任务列表
    total_splits: int = 0
    completed_splits: int = 0

    # 执行结果
    execution_results: List[Dict] = None
    total_filled_amount: float = 0.0
    average_price: float = 0.0

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)
        if self.updated_at is None:
            self.updated_at = self.created_at
        if self.split_tasks is None:
            self.split_tasks = []
        if self.execution_results is None:
            self.execution_results = []


@dataclass
class SplitTask:
    """拆单任务"""
    task_id: str
    order_id: str
    split_index: int
    long_exchange: str
    long_symbol: str
    long_is_spot: bool
    short_exchange: str
    short_symbol: str
    short_is_spot: bool
    amount: float
    amount_currency: str
    # 新增：触发条件信息
    conditions: List[Dict] = None  # 触发条件列表（从原始订单继承）
    execution_type: ExecutionType = ExecutionType.IMMEDIATE  # 执行类型
    status: str = "pending"  # pending, executing, completed, failed
    created_at: Optional[datetime] = None
    executed_at: Optional[datetime] = None
    execution_result: Optional[Dict] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)
        if self.conditions is None:
            self.conditions = []


class OrderCacheManager:
    """订单缓存管理器"""

    def __init__(self):
        """初始化订单缓存管理器"""
        self.orders: Dict[str, HedgingOrder] = {}
        self.split_tasks: Dict[str, SplitTask] = {}
        self._lock = asyncio.Lock()

        logger.info("订单缓存管理器初始化完成")

    async def save_order(self, order: HedgingOrder) -> bool:
        """保存订单到缓存

        Args:
            order: 套保订单

        Returns:
            bool: 是否保存成功
        """
        try:
            async with self._lock:
                order.updated_at = datetime.now(timezone.utc)
                self.orders[order.order_id] = order
                logger.info(f"订单已保存到缓存: {order.order_id}")
                return True

        except Exception as e:
            logger.error(f"保存订单到缓存失败: {str(e)}")
            return False

    async def get_order(self, order_id: str) -> Optional[HedgingOrder]:
        """获取订单

        Args:
            order_id: 订单ID

        Returns:
            HedgingOrder: 订单对象，不存在返回None
        """
        try:
            async with self._lock:
                return self.orders.get(order_id)

        except Exception as e:
            logger.error(f"获取订单失败: {str(e)}")
            return None

    async def update_order_status(self, order_id: str, status: OrderStatus) -> bool:
        """更新订单状态

        Args:
            order_id: 订单ID
            status: 新状态

        Returns:
            bool: 是否更新成功
        """
        try:
            async with self._lock:
                if order_id in self.orders:
                    self.orders[order_id].status = status
                    self.orders[order_id].updated_at = datetime.now(timezone.utc)
                    logger.info(f"订单状态已更新: {order_id} -> {status.value}")
                    return True
                else:
                    logger.warning(f"订单不存在: {order_id}")
                    return False

        except Exception as e:
            logger.error(f"更新订单状态失败: {str(e)}")
            return False

    async def save_split_tasks(self, order_id: str, split_tasks: List[SplitTask]) -> bool:
        """保存拆单任务

        Args:
            order_id: 订单ID
            split_tasks: 拆单任务列表

        Returns:
            bool: 是否保存成功
        """
        try:
            async with self._lock:
                # 保存拆单任务到缓存
                for task in split_tasks:
                    self.split_tasks[task.task_id] = task

                # 更新订单的拆单信息
                if order_id in self.orders:
                    order = self.orders[order_id]
                    order.split_tasks = [asdict(task) for task in split_tasks]
                    order.total_splits = len(split_tasks)
                    order.status = OrderStatus.SPLIT_COMPLETED
                    order.updated_at = datetime.now(timezone.utc)

                logger.info(f"拆单任务已保存: {order_id}, 共 {len(split_tasks)} 个任务")
                return True

        except Exception as e:
            logger.error(f"保存拆单任务失败: {str(e)}")
            return False

    async def get_pending_split_tasks(self) -> List[SplitTask]:
        """获取待执行的拆单任务

        Returns:
            List[SplitTask]: 待执行的拆单任务列表
        """
        try:
            async with self._lock:
                pending_tasks = []
                for task in self.split_tasks.values():
                    if task.status == "pending":
                        pending_tasks.append(task)

                logger.debug(f"获取到 {len(pending_tasks)} 个待执行任务")
                return pending_tasks

        except Exception as e:
            logger.error(f"获取待执行任务失败: {str(e)}")
            return []

    async def update_split_task_status(self, task_id: str, status: str,
                                     execution_result: Optional[Dict] = None) -> bool:
        """更新拆单任务状态

        Args:
            task_id: 任务ID
            status: 新状态
            execution_result: 执行结果

        Returns:
            bool: 是否更新成功
        """
        try:
            async with self._lock:
                if task_id in self.split_tasks:
                    task = self.split_tasks[task_id]
                    task.status = status
                    if execution_result:
                        task.execution_result = execution_result
                    if status in ["completed", "failed"]:
                        task.executed_at = datetime.now(timezone.utc)

                    # 更新对应订单的完成数量
                    if status == "completed":
                        await self._update_order_completion(task.order_id)

                    logger.info(f"拆单任务状态已更新: {task_id} -> {status}")
                    return True
                else:
                    logger.warning(f"拆单任务不存在: {task_id}")
                    return False

        except Exception as e:
            logger.error(f"更新拆单任务状态失败: {str(e)}")
            return False

    async def _update_order_completion(self, order_id: str):
        """更新订单完成情况

        Args:
            order_id: 订单ID
        """
        try:
            if order_id in self.orders:
                order = self.orders[order_id]

                # 统计已完成的拆单任务
                completed_count = 0
                total_filled = 0.0

                for task_data in order.split_tasks:
                    task_id = task_data.get('task_id')
                    if task_id in self.split_tasks:
                        task = self.split_tasks[task_id]
                        if task.status == "completed":
                            completed_count += 1
                            if task.execution_result:
                                total_filled += task.execution_result.get('filled_amount', 0)

                order.completed_splits = completed_count
                order.total_filled_amount = total_filled

                # 判断订单是否完全完成
                if completed_count == order.total_splits:
                    order.status = OrderStatus.COMPLETED
                elif completed_count > 0:
                    order.status = OrderStatus.PARTIALLY_FILLED

                order.updated_at = datetime.now(timezone.utc)

        except Exception as e:
            logger.error(f"更新订单完成情况失败: {str(e)}")

    async def get_orders_by_status(self, status: OrderStatus) -> List[HedgingOrder]:
        """根据状态获取订单列表

        Args:
            status: 订单状态

        Returns:
            List[HedgingOrder]: 订单列表
        """
        try:
            async with self._lock:
                orders = []
                for order in self.orders.values():
                    if order.status == status:
                        orders.append(order)

                return orders

        except Exception as e:
            logger.error(f"根据状态获取订单失败: {str(e)}")
            return []

    async def get_order_summary(self, order_id: str) -> Optional[Dict]:
        """获取订单摘要信息

        Args:
            order_id: 订单ID

        Returns:
            Dict: 订单摘要信息
        """
        try:
            order = await self.get_order(order_id)
            if not order:
                return None

            return {
                'order_id': order.order_id,
                'status': order.status.value,
                'execution_type': order.execution_type.value,
                'amount': order.amount,
                'amount_currency': order.amount_currency,
                'total_splits': order.total_splits,
                'completed_splits': order.completed_splits,
                'total_filled_amount': order.total_filled_amount,
                'progress': order.completed_splits / order.total_splits if order.total_splits > 0 else 0,
                'created_at': order.created_at.isoformat() if order.created_at else None,
                'updated_at': order.updated_at.isoformat() if order.updated_at else None
            }

        except Exception as e:
            logger.error(f"获取订单摘要失败: {str(e)}")
            return None

    async def cleanup_old_orders(self, hours: int = 24):
        """清理旧订单

        Args:
            hours: 保留最近多少小时的订单
        """
        try:
            async with self._lock:
                cutoff_time = datetime.now(timezone.utc).timestamp() - (hours * 3600)

                orders_to_remove = []
                for order_id, order in self.orders.items():
                    if order.created_at and order.created_at.timestamp() < cutoff_time:
                        if order.status in [OrderStatus.COMPLETED, OrderStatus.FAILED, OrderStatus.CANCELLED]:
                            orders_to_remove.append(order_id)

                for order_id in orders_to_remove:
                    del self.orders[order_id]
                    logger.info(f"清理旧订单: {order_id}")

                logger.info(f"清理了 {len(orders_to_remove)} 个旧订单")

        except Exception as e:
            logger.error(f"清理旧订单失败: {str(e)}")


# 全局单例
_order_cache_manager = None

def get_order_cache_manager() -> OrderCacheManager:
    """获取订单缓存管理器单例"""
    global _order_cache_manager
    if _order_cache_manager is None:
        _order_cache_manager = OrderCacheManager()
    return _order_cache_manager
