"""
条件检查服务
负责检查套保订单的触发条件，从策略引擎中提取条件检查逻辑
"""
import logging
from typing import List, Dict, Optional
from enum import Enum

logger = logging.getLogger(__name__)


class TriggerType(Enum):
    """触发条件类型"""
    PRICE_DIFFERENCE = "price_difference"  # 价差
    FUNDING_RATE_DIFF = "funding_rate_diff"  # 资金费率差异
    BASIS_RATE = "basis_rate"  # 基差率
    CUSTOM = "custom"  # 自定义条件


class ConditionChecker:
    """条件检查服务"""

    def __init__(self):
        """初始化条件检查服务"""
        self.market_data_cache = {}  # 市场数据缓存
        logger.info("条件检查服务初始化完成")

    async def check_conditions(self, conditions: List[Dict],
                             long_exchange: str, long_symbol: str, long_is_spot: bool,
                             short_exchange: str, short_symbol: str, short_is_spot: bool) -> bool:
        """检查是否满足所有触发条件（AND逻辑）

        Args:
            conditions: 触发条件列表
            long_exchange: 多头交易所
            long_symbol: 多头交易对
            long_is_spot: 多头是否为现货
            short_exchange: 空头交易所
            short_symbol: 空头交易对
            short_is_spot: 空头是否为现货

        Returns:
            bool: 是否满足所有条件
        """
        try:
            # 如果没有条件，直接返回True（立即执行）
            if not conditions:
                logger.debug("无触发条件，立即执行")
                return True

            logger.debug(f"检查 {len(conditions)} 个触发条件")

            # 检查每个条件（AND逻辑）
            for condition in conditions:
                condition_met = await self._check_single_condition(
                    condition, long_exchange, long_symbol, long_is_spot,
                    short_exchange, short_symbol, short_is_spot
                )

                if not condition_met:
                    logger.debug(f"条件未满足: {condition}")
                    return False

            logger.info("所有触发条件均已满足")
            return True

        except Exception as e:
            logger.error(f"检查触发条件失败: {str(e)}")
            return False

    async def _check_single_condition(self, condition: Dict,
                                    long_exchange: str, long_symbol: str, long_is_spot: bool,
                                    short_exchange: str, short_symbol: str, short_is_spot: bool) -> bool:
        """检查单个触发条件

        Args:
            condition: 单个触发条件
            long_exchange: 多头交易所
            long_symbol: 多头交易对
            long_is_spot: 多头是否为现货
            short_exchange: 空头交易所
            short_symbol: 空头交易对
            short_is_spot: 空头是否为现货

        Returns:
            bool: 是否满足条件
        """
        try:
            condition_type = condition.get('type')
            trigger_value = condition.get('trigger_value')
            comparison_operator = condition.get('comparison_operator', '>')

            if not condition_type or trigger_value is None:
                logger.warning(f"条件配置不完整: {condition}")
                return False

            if condition_type == TriggerType.PRICE_DIFFERENCE.value:
                return await self._check_price_difference(
                    trigger_value, comparison_operator,
                    long_exchange, long_symbol, long_is_spot,
                    short_exchange, short_symbol, short_is_spot
                )
            elif condition_type == TriggerType.FUNDING_RATE_DIFF.value:
                return await self._check_funding_rate_diff(
                    trigger_value, comparison_operator,
                    long_exchange, long_symbol, long_is_spot,
                    short_exchange, short_symbol, short_is_spot
                )
            elif condition_type == TriggerType.BASIS_RATE.value:
                return await self._check_basis_rate(
                    trigger_value, comparison_operator,
                    long_exchange, long_symbol, long_is_spot,
                    short_exchange, short_symbol, short_is_spot
                )
            elif condition_type == TriggerType.CUSTOM.value:
                return await self._check_custom_condition(condition)
            else:
                logger.warning(f"未知的触发条件类型: {condition_type}")
                return False

        except Exception as e:
            logger.error(f"检查单个触发条件失败: {str(e)}")
            return False

    async def _check_price_difference(self, trigger_value: float, comparison_operator: str,
                                    long_exchange: str, long_symbol: str, long_is_spot: bool,
                                    short_exchange: str, short_symbol: str, short_is_spot: bool) -> bool:
        """检查价差条件"""
        try:
            # 获取多头价格
            long_price = await self._get_market_price(long_exchange, long_symbol, long_is_spot)
            # 获取空头价格
            short_price = await self._get_market_price(short_exchange, short_symbol, short_is_spot)

            if long_price is None or short_price is None:
                logger.warning("无法获取价格数据")
                return False

            # 计算价差
            price_diff = long_price - short_price

            # 检查触发条件
            result = self._compare_value(price_diff, trigger_value, comparison_operator)
            logger.debug(f"价差检查: {price_diff} {comparison_operator} {trigger_value} = {result}")
            return result

        except Exception as e:
            logger.error(f"检查价差条件失败: {str(e)}")
            return False

    async def _check_funding_rate_diff(self, trigger_value: float, comparison_operator: str,
                                     long_exchange: str, long_symbol: str, long_is_spot: bool,
                                     short_exchange: str, short_symbol: str, short_is_spot: bool) -> bool:
        """检查资金费率差异条件"""
        try:
            # 获取多头资金费率
            long_funding_rate = await self._get_funding_rate(long_exchange, long_symbol, long_is_spot)
            # 获取空头资金费率
            short_funding_rate = await self._get_funding_rate(short_exchange, short_symbol, short_is_spot)

            if long_funding_rate is None or short_funding_rate is None:
                logger.warning("无法获取资金费率数据")
                return False

            # 计算资金费率差异
            funding_diff = long_funding_rate - short_funding_rate

            # 检查触发条件
            result = self._compare_value(funding_diff, trigger_value, comparison_operator)
            logger.debug(f"资金费率差异检查: {funding_diff} {comparison_operator} {trigger_value} = {result}")
            return result

        except Exception as e:
            logger.error(f"检查资金费率差异条件失败: {str(e)}")
            return False

    async def _check_basis_rate(self, trigger_value: float, comparison_operator: str,
                              long_exchange: str, long_symbol: str, long_is_spot: bool,
                              short_exchange: str, short_symbol: str, short_is_spot: bool) -> bool:
        """检查基差率条件"""
        try:
            # 获取现货价格（通常是多头）
            spot_price = await self._get_market_price(long_exchange, long_symbol, True)
            # 获取期货价格（通常是空头）
            futures_price = await self._get_market_price(short_exchange, short_symbol, False)

            if spot_price is None or futures_price is None:
                logger.warning("无法获取现货或期货价格数据")
                return False

            # 计算基差率
            basis_rate = (futures_price - spot_price) / spot_price * 100

            # 检查触发条件
            result = self._compare_value(basis_rate, trigger_value, comparison_operator)
            logger.debug(f"基差率检查: {basis_rate}% {comparison_operator} {trigger_value}% = {result}")
            return result

        except Exception as e:
            logger.error(f"检查基差率条件失败: {str(e)}")
            return False

    async def _check_custom_condition(self, condition: Dict) -> bool:
        """检查自定义条件"""
        try:
            # TODO: 实现自定义条件检查逻辑
            logger.warning("自定义条件检查尚未实现")
            return True

        except Exception as e:
            logger.error(f"检查自定义条件失败: {str(e)}")
            return False

    def _compare_value(self, actual_value: float, trigger_value: float, operator: str) -> bool:
        """比较数值

        Args:
            actual_value: 实际值
            trigger_value: 触发值
            operator: 比较运算符

        Returns:
            bool: 比较结果
        """
        try:
            if operator == '>':
                return actual_value > trigger_value
            elif operator == '<':
                return actual_value < trigger_value
            elif operator == '>=':
                return actual_value >= trigger_value
            elif operator == '<=':
                return actual_value <= trigger_value
            elif operator == '==':
                return abs(actual_value - trigger_value) < 1e-8  # 浮点数相等比较
            else:
                logger.warning(f"未知的比较运算符: {operator}")
                return False

        except Exception as e:
            logger.error(f"数值比较失败: {str(e)}")
            return False

    async def _get_market_price(self, exchange: str, symbol: str, is_spot: bool) -> Optional[float]:
        """获取市场价格

        Args:
            exchange: 交易所
            symbol: 交易对
            is_spot: 是否为现货

        Returns:
            Optional[float]: 价格，获取失败返回None
        """
        try:
            # TODO: 实现真实的市场数据获取
            # 这里应该调用实际的市场数据接口

            # 模拟价格数据
            cache_key = f"{exchange}_{symbol}_{is_spot}"
            if cache_key in self.market_data_cache:
                return self.market_data_cache[cache_key].get('price')

            # 如果缓存中没有数据，返回模拟价格
            logger.warning(f"使用模拟价格数据: {cache_key}")
            return 50000.0  # 模拟BTC价格

        except Exception as e:
            logger.error(f"获取市场价格失败: {str(e)}")
            return None

    async def _get_funding_rate(self, exchange: str, symbol: str, is_spot: bool) -> Optional[float]:
        """获取资金费率

        Args:
            exchange: 交易所
            symbol: 交易对
            is_spot: 是否为现货

        Returns:
            Optional[float]: 资金费率，获取失败返回None
        """
        try:
            # 现货没有资金费率
            if is_spot:
                return 0.0

            # TODO: 实现真实的资金费率获取
            # 这里应该调用实际的交易所API

            # 模拟资金费率数据
            cache_key = f"{exchange}_{symbol}_{is_spot}"
            if cache_key in self.market_data_cache:
                return self.market_data_cache[cache_key].get('funding_rate')

            # 如果缓存中没有数据，返回模拟资金费率
            logger.warning(f"使用模拟资金费率数据: {cache_key}")
            return 0.0001  # 模拟0.01%的资金费率

        except Exception as e:
            logger.error(f"获取资金费率失败: {str(e)}")
            return None

    def update_market_data(self, exchange: str, symbol: str, is_spot: bool,
                          price: float, funding_rate: Optional[float] = None):
        """更新市场数据缓存

        Args:
            exchange: 交易所
            symbol: 交易对
            is_spot: 是否为现货
            price: 价格
            funding_rate: 资金费率（可选）
        """
        try:
            cache_key = f"{exchange}_{symbol}_{is_spot}"
            self.market_data_cache[cache_key] = {
                'price': price,
                'funding_rate': funding_rate,
                'updated_at': None  # 可以添加时间戳
            }
            logger.info(f"更新市场数据: {cache_key}")

        except Exception as e:
            logger.error(f"更新市场数据失败: {str(e)}")


# 全局单例
_condition_checker = None

def get_condition_checker() -> ConditionChecker:
    """获取条件检查服务单例"""
    global _condition_checker
    if _condition_checker is None:
        _condition_checker = ConditionChecker()
    return _condition_checker
