#!/bin/bash

# Crypt Carry Web Backend 启动脚本
# 专门用于启动后端服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2

    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $port 已被占用 ($service)"
        read -p "是否要杀死占用进程? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            local pid=$(lsof -Pi :$port -sTCP:LISTEN -t)
            kill -9 $pid 2>/dev/null || true
            log_success "已杀死进程 $pid"
        else
            log_error "端口冲突，退出启动"
            exit 1
        fi
    fi
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WEB_DIR="$SCRIPT_DIR"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# 创建日志目录（按策略类型分类）
HEDGING_LOG_DIR="$PROJECT_ROOT/logs/hedging"
FUNDING_RATE_LOG_DIR="$PROJECT_ROOT/logs/funding_rate"
WEB_LOG_DIR="$PROJECT_ROOT/logs/web"

mkdir -p "$HEDGING_LOG_DIR"
mkdir -p "$FUNDING_RATE_LOG_DIR"
mkdir -p "$WEB_LOG_DIR"

log_info "Crypt Carry Web Backend 启动脚本"
log_info "项目根目录: $PROJECT_ROOT"
log_info "Web目录: $WEB_DIR"
log_info "日志目录: $PROJECT_ROOT/logs/"

# 检查必要的命令
log_info "检查依赖..."
check_command "python3"

# 检查端口
log_info "检查端口占用..."
check_port 5001 "后端服务"

# 检查conda环境
log_info "检查conda环境..."

# 检查是否安装了conda或已在conda环境中
if ! command -v conda &> /dev/null && [ -z "$CONDA_PREFIX" ]; then
    log_error "未检测到conda，请先安装conda"
    log_info "安装指南: https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

# 检查是否在conda环境中
if [ -z "$CONDA_DEFAULT_ENV" ]; then
    log_error "未激活conda环境"
    log_info "请先激活conda环境："
    log_info "  conda activate your-env-name"
    log_info "或创建新环境："
    log_info "  conda create -n crypt_carry python=3.9"
    log_info "  conda activate crypt_carry"
    exit 1
else
    log_success "检测到conda环境: $CONDA_DEFAULT_ENV"
fi

# 安装后端依赖
log_info "检查后端依赖..."
cd "$PROJECT_ROOT"
if [ ! -f "requirements.txt" ]; then
    log_error "requirements.txt 不存在于项目根目录"
    exit 1
fi

# 检查是否需要安装依赖
if ! pip list 2>/dev/null | grep -q flask; then
    log_info "安装后端依赖..."
    pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
    log_success "后端依赖安装完成"
else
    log_info "后端依赖已安装"
fi

# 启动服务的函数
start_backend() {
    log_info "启动后端服务..."
    cd "$WEB_DIR"

    # 设置环境变量
    export FLASK_APP=app.py
    export FLASK_ENV=development
    export FLASK_DEBUG=1
    export FLASK_PORT=5001
    export PYTHONPATH="$PROJECT_ROOT/src:$PYTHONPATH"
    
    # 设置日志路径环境变量
    export HEDGING_LOG_DIR="$HEDGING_LOG_DIR"
    export FUNDING_RATE_LOG_DIR="$FUNDING_RATE_LOG_DIR"
    export WEB_LOG_DIR="$WEB_LOG_DIR"

    # 启动Flask应用
    python app.py > "$WEB_LOG_DIR/backend.log" 2>&1 &
    BACKEND_PID=$!

    # 等待服务启动
    sleep 3

    # 检查服务是否启动成功
    if curl -s http://localhost:5001/health > /dev/null 2>&1; then
        log_success "后端服务启动成功 (PID: $BACKEND_PID)"
        echo $BACKEND_PID > "$WEB_LOG_DIR/backend.pid"
        log_info "后端服务: http://localhost:5001"
        log_info "API文档: http://localhost:5001/api/docs"
        log_info "健康检查: http://localhost:5001/health"
        log_info "日志文件: $WEB_LOG_DIR/backend.log"
    else
        log_error "后端服务启动失败"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
}

# 停止服务的函数
stop_backend() {
    log_info "正在停止后端服务..."

    # 停止后端服务
    if [ -f "$WEB_LOG_DIR/backend.pid" ]; then
        BACKEND_PID=$(cat "$WEB_LOG_DIR/backend.pid")
        kill $BACKEND_PID 2>/dev/null || true
        rm -f "$WEB_LOG_DIR/backend.pid"
        log_success "后端服务已停止"
    fi

    # 杀死可能残留的进程
    pkill -f "python.*app.py" 2>/dev/null || true
}

# 信号处理
trap stop_backend EXIT INT TERM

# 解析命令行参数
case "${1:-start}" in
    "start")
        start_backend
        log_info ""
        log_info "按 Ctrl+C 停止后端服务"

        # 等待用户中断
        while true; do
            sleep 1
        done
        ;;

    "stop")
        stop_backend
        ;;

    "restart")
        stop_backend
        sleep 2
        start_backend
        log_success "后端服务重启完成!"
        ;;

    "status")
        log_info "检查后端服务状态..."
        if curl -s http://localhost:5001/health > /dev/null 2>&1; then
            log_success "后端服务运行中 (http://localhost:5001)"
        else
            log_error "后端服务未运行"
        fi
        ;;

    "logs")
        log_info "查看后端服务日志..."
        if [ -f "$WEB_LOG_DIR/backend.log" ]; then
            tail -f "$WEB_LOG_DIR/backend.log"
        else
            log_error "日志文件不存在: $WEB_LOG_DIR/backend.log"
        fi
        ;;

    "help"|"-h"|"--help")
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  start    启动后端服务 (默认)"
        echo "  stop     停止后端服务"
        echo "  restart  重启后端服务"
        echo "  status   检查服务状态"
        echo "  logs     查看服务日志"
        echo "  help     显示此帮助信息"
        echo ""
        echo "示例:"
        echo "  $0 start    # 启动服务"
        echo "  $0 status   # 检查状态"
        echo "  $0 logs     # 查看日志"
        ;;

    *)
        log_error "未知命令: $1"
        log_info "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac
